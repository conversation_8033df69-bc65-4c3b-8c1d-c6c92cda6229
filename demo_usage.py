#!/usr/bin/env python3
"""
Demo script showing how to use the fixed churn analysis code
"""

from churn_analysis import Churn<PERSON>naly<PERSON>, run_complete_analysis

def demo_basic_usage():
    """
    Demonstrate basic usage of the ChurnAnalyzer
    """
    print("🎯 Demo: Basic Usage of ChurnAnalyzer")
    print("="*50)
    
    # Initialize the analyzer
    analyzer = ChurnAnalyzer("Telco_Customer_Churn.xlxs")
    
    # Step-by-step analysis
    print("\n1. Loading data...")
    if analyzer.load_data():
        print("   ✅ Data loaded successfully!")
        
        print("\n2. Exploring data...")
        analyzer.explore_data()
        
        print("\n3. Preprocessing data...")
        if analyzer.preprocess_data():
            print("   ✅ Data preprocessed successfully!")
            
            print("\n4. Training models...")
            if analyzer.train_models():
                print("   ✅ Models trained successfully!")
                
                print("\n5. Calculating detailed metrics...")
                analyzer.calculate_detailed_metrics()
                
                print("\n6. Creating visualizations...")
                analyzer.create_visualizations()
                
                print("\n7. Generating summary report...")
                analyzer.generate_summary_report()
                
                return analyzer
    
    return None

def demo_quick_analysis():
    """
    Demonstrate the quick analysis function
    """
    print("\n\n🚀 Demo: Quick Complete Analysis")
    print("="*50)
    
    # Run complete analysis in one function call
    analyzer = run_complete_analysis("Telco_Customer_Churn.xlxs")
    
    if analyzer:
        print("\n📊 Quick Results Summary:")
        print(f"Models trained: {list(analyzer.models.keys())}")
        print(f"Accuracies: {analyzer.accuracies}")
        
        # Get best model
        best_model_name, best_accuracy = max(analyzer.accuracies.items(), key=lambda x: x[1])
        print(f"Best model: {best_model_name} with {best_accuracy:.4f} accuracy")
        
        return analyzer
    
    return None

def demo_custom_target():
    """
    Demonstrate analysis with custom target column
    """
    print("\n\n🎯 Demo: Custom Target Column")
    print("="*50)
    
    # If your dataset has a different churn column name
    # analyzer = run_complete_analysis("your_data.xlsx", target_column="your_churn_column")
    print("To use a custom target column, call:")
    print("analyzer = run_complete_analysis('data.xlsx', target_column='custom_churn_col')")

if __name__ == "__main__":
    print("🎬 Churn Analysis Demo")
    print("="*60)
    
    # Check if data file exists
    import os
    if not os.path.exists("Telco_Customer_Churn.xlxs"):
        print("❌ Demo data file not found!")
        print("Please ensure 'Telco_Customer_Churn.xlxs' is in the current directory.")
        exit(1)
    
    # Run demos
    try:
        # Demo 1: Step-by-step usage
        analyzer1 = demo_basic_usage()
        
        # Demo 2: Quick analysis
        analyzer2 = demo_quick_analysis()
        
        # Demo 3: Custom target info
        demo_custom_target()
        
        print("\n🎉 Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        print("Please install required packages: pip install -r requirements.txt")

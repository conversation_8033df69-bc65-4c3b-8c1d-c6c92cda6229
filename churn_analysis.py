#!/usr/bin/env python3
"""
Customer Churn Analysis Script
This script performs comprehensive churn analysis including:
- Data loading and preprocessing
- Model training and evaluation
- Accuracy calculation
- Visualization generation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.impute import SimpleImputer
import warnings
warnings.filterwarnings('ignore')

class ChurnAnalyzer:
    """
    A comprehensive class for customer churn analysis
    """
    
    def __init__(self, data_path):
        """
        Initialize the ChurnAnalyzer
        
        Args:
            data_path (str): Path to the Excel file containing churn data
        """
        self.data_path = data_path
        self.df = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.predictions = {}
        self.accuracies = {}
        
    def load_data(self):
        """
        Load data from Excel file with error handling
        """
        try:
            print("Loading data from Excel file...")
            self.df = pd.read_excel(self.data_path)
            print(f"Data loaded successfully. Shape: {self.df.shape}")
            print(f"Columns: {list(self.df.columns)}")
            return True
        except FileNotFoundError:
            print(f"Error: File {self.data_path} not found!")
            return False
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return False
    
    def explore_data(self):
        """
        Perform initial data exploration
        """
        if self.df is None:
            print("No data loaded. Please load data first.")
            return
        
        print("\n=== DATA EXPLORATION ===")
        print(f"Dataset shape: {self.df.shape}")
        print(f"\nData types:\n{self.df.dtypes}")
        print(f"\nMissing values:\n{self.df.isnull().sum()}")
        print(f"\nFirst few rows:\n{self.df.head()}")
        
        # Check for churn column (common names)
        churn_columns = ['Churn', 'churn', 'Churn_Label', 'Customer_Status', 'Exited']
        self.churn_column = None
        
        for col in churn_columns:
            if col in self.df.columns:
                self.churn_column = col
                break
        
        if self.churn_column:
            print(f"\nChurn column found: {self.churn_column}")
            print(f"Churn distribution:\n{self.df[self.churn_column].value_counts()}")
        else:
            print("\nWarning: No standard churn column found. Please specify manually.")
            print("Available columns:", list(self.df.columns))
    
    def preprocess_data(self, target_column=None):
        """
        Preprocess the data for machine learning
        
        Args:
            target_column (str): Name of the target column (churn indicator)
        """
        if self.df is None:
            print("No data loaded. Please load data first.")
            return False
        
        # Use detected churn column or provided target column
        if target_column:
            self.churn_column = target_column
        
        if not self.churn_column or self.churn_column not in self.df.columns:
            print("Error: Target column not specified or not found in data.")
            return False
        
        print(f"\n=== DATA PREPROCESSING ===")
        print(f"Using '{self.churn_column}' as target variable")
        
        # Create a copy for preprocessing
        df_processed = self.df.copy()
        
        # Handle target variable
        y = df_processed[self.churn_column]
        
        # Convert target to binary if needed
        if y.dtype == 'object':
            le_target = LabelEncoder()
            y = le_target.fit_transform(y)
            print(f"Target variable encoded. Classes: {le_target.classes_}")
        
        # Remove target from features
        X = df_processed.drop(columns=[self.churn_column])
        
        # Handle categorical variables
        categorical_columns = X.select_dtypes(include=['object']).columns
        print(f"Categorical columns found: {list(categorical_columns)}")
        
        # Encode categorical variables
        label_encoders = {}
        for col in categorical_columns:
            le = LabelEncoder()
            # Handle missing values in categorical columns
            X[col] = X[col].fillna('Unknown')
            X[col] = le.fit_transform(X[col])
            label_encoders[col] = le
        
        # Handle numerical missing values
        numerical_columns = X.select_dtypes(include=[np.number]).columns
        if len(numerical_columns) > 0:
            imputer = SimpleImputer(strategy='median')
            X[numerical_columns] = imputer.fit_transform(X[numerical_columns])
        
        # Split the data
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale the features
        scaler = StandardScaler()
        self.X_train_scaled = scaler.fit_transform(self.X_train)
        self.X_test_scaled = scaler.transform(self.X_test)
        
        print(f"Training set shape: {self.X_train.shape}")
        print(f"Test set shape: {self.X_test.shape}")
        print(f"Class distribution in training set: {np.bincount(self.y_train)}")
        
        return True
    
    def train_models(self):
        """
        Train multiple machine learning models
        """
        if self.X_train is None:
            print("Data not preprocessed. Please preprocess data first.")
            return False
        
        print("\n=== MODEL TRAINING ===")
        
        # Define models
        models_to_train = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        # Train models
        for name, model in models_to_train.items():
            print(f"Training {name}...")
            
            if name == 'Logistic Regression':
                # Use scaled features for logistic regression
                model.fit(self.X_train_scaled, self.y_train)
                predictions = model.predict(self.X_test_scaled)
                pred_proba = model.predict_proba(self.X_test_scaled)[:, 1]
            else:
                # Use original features for tree-based models
                model.fit(self.X_train, self.y_train)
                predictions = model.predict(self.X_test)
                pred_proba = model.predict_proba(self.X_test)[:, 1]
            
            # Store results
            self.models[name] = model
            self.predictions[name] = predictions
            
            # Calculate accuracy
            accuracy = accuracy_score(self.y_test, predictions)
            self.accuracies[name] = accuracy
            
            print(f"{name} Accuracy: {accuracy:.4f}")
        
        return True
    
    def calculate_detailed_metrics(self):
        """
        Calculate detailed performance metrics for all models
        """
        if not self.models:
            print("No models trained. Please train models first.")
            return
        
        print("\n=== DETAILED PERFORMANCE METRICS ===")
        
        for name, model in self.models.items():
            print(f"\n--- {name} ---")
            predictions = self.predictions[name]
            
            # Basic metrics
            accuracy = accuracy_score(self.y_test, predictions)
            
            # Get probabilities for AUC calculation
            if name == 'Logistic Regression':
                pred_proba = model.predict_proba(self.X_test_scaled)[:, 1]
            else:
                pred_proba = model.predict_proba(self.X_test)[:, 1]
            
            auc_score = roc_auc_score(self.y_test, pred_proba)
            
            print(f"Accuracy: {accuracy:.4f}")
            print(f"AUC Score: {auc_score:.4f}")
            print(f"\nClassification Report:")
            print(classification_report(self.y_test, predictions))

#!/usr/bin/env python3
"""
Customer Churn Analysis Script
This script performs comprehensive churn analysis including:
- Data loading and preprocessing
- Model training and evaluation
- Accuracy calculation
- Visualization generation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.impute import SimpleImputer
import warnings
warnings.filterwarnings('ignore')

class ChurnAnalyzer:
    """
    A comprehensive class for customer churn analysis
    """
    
    def __init__(self, data_path):
        """
        Initialize the ChurnAnalyzer
        
        Args:
            data_path (str): Path to the Excel file containing churn data
        """
        self.data_path = data_path
        self.df = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.predictions = {}
        self.pred_probabilities = {}  # Store prediction probabilities
        self.accuracies = {}
        self.scaler = None  # Store scaler for future use
        self.label_encoders = {}  # Store label encoders
        
    def load_data(self):
        """
        Load data from Excel file with error handling
        """
        try:
            print("Loading data from Excel file...")
            self.df = pd.read_excel(self.data_path)
            print(f"Data loaded successfully. Shape: {self.df.shape}")
            print(f"Columns: {list(self.df.columns)}")
            return True
        except FileNotFoundError:
            print(f"Error: File {self.data_path} not found!")
            return False
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return False
    
    def explore_data(self):
        """
        Perform initial data exploration
        """
        if self.df is None:
            print("No data loaded. Please load data first.")
            return
        
        print("\n=== DATA EXPLORATION ===")
        print(f"Dataset shape: {self.df.shape}")
        print(f"\nData types:\n{self.df.dtypes}")
        print(f"\nMissing values:\n{self.df.isnull().sum()}")
        print(f"\nFirst few rows:\n{self.df.head()}")
        
        # Check for churn column (common names)
        churn_columns = ['Churn', 'churn', 'Churn_Label', 'Customer_Status', 'Exited']
        self.churn_column = None
        
        for col in churn_columns:
            if col in self.df.columns:
                self.churn_column = col
                break
        
        if self.churn_column:
            print(f"\nChurn column found: {self.churn_column}")
            print(f"Churn distribution:\n{self.df[self.churn_column].value_counts()}")
        else:
            print("\nWarning: No standard churn column found. Please specify manually.")
            print("Available columns:", list(self.df.columns))
    
    def preprocess_data(self, target_column=None):
        """
        Preprocess the data for machine learning
        
        Args:
            target_column (str): Name of the target column (churn indicator)
        """
        if self.df is None:
            print("No data loaded. Please load data first.")
            return False
        
        # Use detected churn column or provided target column
        if target_column:
            self.churn_column = target_column
        
        if not self.churn_column or self.churn_column not in self.df.columns:
            print("Error: Target column not specified or not found in data.")
            return False
        
        print(f"\n=== DATA PREPROCESSING ===")
        print(f"Using '{self.churn_column}' as target variable")
        
        # Create a copy for preprocessing
        df_processed = self.df.copy()
        
        # Handle target variable
        y = df_processed[self.churn_column]
        
        # Convert target to binary if needed
        if y.dtype == 'object':
            le_target = LabelEncoder()
            y = le_target.fit_transform(y)
            print(f"Target variable encoded. Classes: {le_target.classes_}")
        
        # Remove target from features
        X = df_processed.drop(columns=[self.churn_column])
        
        # Handle categorical variables
        categorical_columns = X.select_dtypes(include=['object']).columns
        print(f"Categorical columns found: {list(categorical_columns)}")
        
        # Encode categorical variables
        self.label_encoders = {}
        for col in categorical_columns:
            le = LabelEncoder()
            # Handle missing values in categorical columns
            X[col] = X[col].fillna('Unknown')
            X[col] = le.fit_transform(X[col])
            self.label_encoders[col] = le
        
        # Handle numerical missing values
        numerical_columns = X.select_dtypes(include=[np.number]).columns
        if len(numerical_columns) > 0:
            imputer = SimpleImputer(strategy='median')
            X[numerical_columns] = imputer.fit_transform(X[numerical_columns])
        
        # Split the data
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale the features
        self.scaler = StandardScaler()
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
        print(f"Training set shape: {self.X_train.shape}")
        print(f"Test set shape: {self.X_test.shape}")
        print(f"Class distribution in training set: {np.bincount(self.y_train)}")
        
        return True
    
    def train_models(self):
        """
        Train multiple machine learning models
        """
        if self.X_train is None:
            print("Data not preprocessed. Please preprocess data first.")
            return False
        
        print("\n=== MODEL TRAINING ===")
        
        # Define models
        models_to_train = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        # Train models
        for name, model in models_to_train.items():
            print(f"Training {name}...")
            
            if name == 'Logistic Regression':
                # Use scaled features for logistic regression
                model.fit(self.X_train_scaled, self.y_train)
                predictions = model.predict(self.X_test_scaled)
                pred_proba = model.predict_proba(self.X_test_scaled)[:, 1]
            else:
                # Use original features for tree-based models
                model.fit(self.X_train, self.y_train)
                predictions = model.predict(self.X_test)
                pred_proba = model.predict_proba(self.X_test)[:, 1]
            
            # Store results
            self.models[name] = model
            self.predictions[name] = predictions
            self.pred_probabilities[name] = pred_proba  # Store probabilities

            # Calculate accuracy
            accuracy = accuracy_score(self.y_test, predictions)
            self.accuracies[name] = accuracy
            
            print(f"{name} Accuracy: {accuracy:.4f}")
        
        return True
    
    def calculate_detailed_metrics(self):
        """
        Calculate detailed performance metrics for all models
        """
        if not self.models:
            print("No models trained. Please train models first.")
            return
        
        print("\n=== DETAILED PERFORMANCE METRICS ===")
        
        for name, model in self.models.items():
            print(f"\n--- {name} ---")
            predictions = self.predictions[name]
            
            # Basic metrics
            accuracy = accuracy_score(self.y_test, predictions)
            
            # Get probabilities for AUC calculation
            if name == 'Logistic Regression':
                pred_proba = model.predict_proba(self.X_test_scaled)[:, 1]
            else:
                pred_proba = model.predict_proba(self.X_test)[:, 1]
            
            auc_score = roc_auc_score(self.y_test, pred_proba)
            
            print(f"Accuracy: {accuracy:.4f}")
            print(f"AUC Score: {auc_score:.4f}")
            print(f"\nClassification Report:")
            print(classification_report(self.y_test, predictions))

    def create_visualizations(self):
        """
        Create comprehensive visualizations for churn analysis
        """
        if not self.models or self.df is None:
            print("No models trained or data loaded. Please train models first.")
            return

        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")

        # Create a figure with multiple subplots
        plt.figure(figsize=(20, 15))

        # 1. Churn Distribution
        plt.subplot(3, 4, 1)
        if self.churn_column and self.churn_column in self.df.columns:
            churn_counts = self.df[self.churn_column].value_counts()
            plt.pie(churn_counts.values, labels=churn_counts.index, autopct='%1.1f%%')
            plt.title('Churn Distribution')

        # 2. Model Accuracy Comparison
        plt.subplot(3, 4, 2)
        models = list(self.accuracies.keys())
        accuracies = list(self.accuracies.values())
        bars = plt.bar(models, accuracies, color=['skyblue', 'lightcoral'])
        plt.title('Model Accuracy Comparison')
        plt.ylabel('Accuracy')
        plt.ylim(0, 1)
        # Add value labels on bars
        for bar, acc in zip(bars, accuracies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom')

        # 3. ROC Curves
        plt.subplot(3, 4, 3)
        for name in self.models.keys():
            pred_proba = self.pred_probabilities[name]
            fpr, tpr, _ = roc_curve(self.y_test, pred_proba)
            auc_score = roc_auc_score(self.y_test, pred_proba)
            plt.plot(fpr, tpr, label=f'{name} (AUC = {auc_score:.3f})')
        plt.plot([0, 1], [0, 1], 'k--', label='Random')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curves')
        plt.legend()

        # 4. Confusion Matrices
        for i, (name, predictions) in enumerate(self.predictions.items()):
            plt.subplot(3, 4, 4 + i)
            cm = confusion_matrix(self.y_test, predictions)
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
            plt.title(f'Confusion Matrix - {name}')
            plt.ylabel('True Label')
            plt.xlabel('Predicted Label')

        # 5. Feature Importance (for Random Forest)
        if 'Random Forest' in self.models:
            plt.subplot(3, 4, 6)
            rf_model = self.models['Random Forest']
            feature_names = [f'Feature_{i}' for i in range(len(rf_model.feature_importances_))]
            # Get top 10 features
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': rf_model.feature_importances_
            }).sort_values('importance', ascending=False).head(10)

            plt.barh(range(len(importance_df)), importance_df['importance'])
            plt.yticks(range(len(importance_df)), importance_df['feature'])
            plt.xlabel('Feature Importance')
            plt.title('Top 10 Feature Importances (Random Forest)')
            plt.gca().invert_yaxis()

        # 6. Data Quality Overview
        plt.subplot(3, 4, 7)
        missing_data = self.df.isnull().sum().sort_values(ascending=False)
        if missing_data.sum() > 0:
            missing_data = missing_data[missing_data > 0].head(10)
            plt.bar(range(len(missing_data)), missing_data.values)
            plt.xticks(range(len(missing_data)), missing_data.index, rotation=45)
            plt.title('Missing Values by Column')
            plt.ylabel('Count')
        else:
            plt.text(0.5, 0.5, 'No Missing Values', ha='center', va='center', transform=plt.gca().transAxes)
            plt.title('Data Quality: No Missing Values')

        # 7. Model Performance Summary
        plt.subplot(3, 4, 8)
        metrics_data = []
        for name in self.models.keys():
            pred_proba = self.pred_probabilities[name]
            auc_score = roc_auc_score(self.y_test, pred_proba)
            accuracy = self.accuracies[name]
            metrics_data.append([name, accuracy, auc_score])

        metrics_df = pd.DataFrame(metrics_data, columns=['Model', 'Accuracy', 'AUC'])
        x = np.arange(len(metrics_df))
        width = 0.35

        plt.bar(x - width/2, metrics_df['Accuracy'], width, label='Accuracy', alpha=0.8)
        plt.bar(x + width/2, metrics_df['AUC'], width, label='AUC', alpha=0.8)
        plt.xlabel('Models')
        plt.ylabel('Score')
        plt.title('Model Performance Metrics')
        plt.xticks(x, metrics_df['Model'])
        plt.legend()
        plt.ylim(0, 1)

        plt.tight_layout()
        plt.savefig('churn_analysis_results.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("Visualizations saved as 'churn_analysis_results.png'")

    def generate_summary_report(self):
        """
        Generate a comprehensive summary report
        """
        if not self.models:
            print("No models trained. Please train models first.")
            return

        print("\n" + "="*60)
        print("           CUSTOMER CHURN ANALYSIS SUMMARY REPORT")
        print("="*60)

        # Dataset Information
        print(f"\n📊 DATASET INFORMATION:")
        print(f"   • Total Records: {len(self.df):,}")
        print(f"   • Total Features: {len(self.df.columns)}")
        print(f"   • Training Set Size: {len(self.y_train):,}")
        print(f"   • Test Set Size: {len(self.y_test):,}")

        if self.churn_column:
            churn_rate = (self.df[self.churn_column].sum() / len(self.df)) * 100
            print(f"   • Overall Churn Rate: {churn_rate:.2f}%")

        # Model Performance
        print(f"\n🎯 MODEL PERFORMANCE:")
        best_model = max(self.accuracies.items(), key=lambda x: x[1])
        print(f"   • Best Performing Model: {best_model[0]} ({best_model[1]:.4f} accuracy)")

        for name, accuracy in self.accuracies.items():
            auc_score = roc_auc_score(self.y_test, self.pred_probabilities[name])
            print(f"   • {name}:")
            print(f"     - Accuracy: {accuracy:.4f}")
            print(f"     - AUC Score: {auc_score:.4f}")

        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if best_model[1] > 0.85:
            print("   • Model performance is excellent (>85% accuracy)")
        elif best_model[1] > 0.75:
            print("   • Model performance is good (>75% accuracy)")
            print("   • Consider feature engineering to improve performance")
        else:
            print("   • Model performance needs improvement (<75% accuracy)")
            print("   • Consider collecting more data or trying different algorithms")

        print(f"\n📈 BUSINESS IMPACT:")
        if self.churn_column:
            total_customers = len(self.df)
            churned_customers = self.df[self.churn_column].sum()
            print(f"   • {churned_customers:,} customers have churned out of {total_customers:,}")
            print(f"   • With {best_model[1]:.1%} accuracy, the model can help identify")
            print(f"     potential churners for retention campaigns")

        print("\n" + "="*60)


def run_complete_analysis(data_path, target_column=None):
    """
    Run the complete churn analysis pipeline

    Args:
        data_path (str): Path to the Excel file
        target_column (str): Name of the target column (optional)
    """
    print("🚀 Starting Customer Churn Analysis...")

    # Initialize analyzer
    analyzer = ChurnAnalyzer(data_path)

    # Load data
    if not analyzer.load_data():
        return None

    # Explore data
    analyzer.explore_data()

    # Preprocess data
    if not analyzer.preprocess_data(target_column):
        return None

    # Train models
    if not analyzer.train_models():
        return None

    # Calculate detailed metrics
    analyzer.calculate_detailed_metrics()

    # Create visualizations
    analyzer.create_visualizations()

    # Generate summary report
    analyzer.generate_summary_report()

    print("\n✅ Analysis completed successfully!")
    return analyzer


if __name__ == "__main__":
    # Configuration
    DATA_FILE = "Telco_Customer_Churn.xlxs"

    # Check if file exists
    import os
    if not os.path.exists(DATA_FILE):
        print(f"❌ Error: Data file '{DATA_FILE}' not found!")
        print("Please ensure the file is in the current directory.")
        exit(1)

    # Run analysis
    try:
        analyzer = run_complete_analysis(DATA_FILE)
        if analyzer:
            print(f"\n📊 Final Results Summary:")
            print(f"Best Model: {max(analyzer.accuracies.items(), key=lambda x: x[1])}")
            print(f"All Accuracies: {analyzer.accuracies}")
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        print("Please check your data file and try again.")

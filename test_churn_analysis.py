#!/usr/bin/env python3
"""
Test script to validate the churn analysis code
"""

import sys
import os

def test_imports():
    """Test if all required imports work"""
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import seaborn as sns
        from sklearn.model_selection import train_test_split
        from sklearn.preprocessing import LabelEncoder, StandardScaler
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.linear_model import LogisticRegression
        from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, roc_curve
        from sklearn.impute import SimpleImputer
        print("✅ All imports successful!")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_file_exists():
    """Test if the data file exists"""
    data_file = "Telco_Customer_Churn.xlxs"
    if os.path.exists(data_file):
        print(f"✅ Data file '{data_file}' found!")
        return True
    else:
        print(f"❌ Data file '{data_file}' not found!")
        return False

def test_code_syntax():
    """Test if the main code file has valid syntax"""
    try:
        with open('churn_analysis.py', 'r') as f:
            code = f.read()
        compile(code, 'churn_analysis.py', 'exec')
        print("✅ Code syntax is valid!")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        return False
    except FileNotFoundError:
        print("❌ churn_analysis.py file not found!")
        return False

def main():
    """Run all tests"""
    print("🧪 Running Churn Analysis Tests...\n")
    
    tests = [
        ("Import Test", test_imports),
        ("File Existence Test", test_file_exists),
        ("Syntax Test", test_code_syntax)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        result = test_func()
        results.append(result)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The code is ready to run.")
    else:
        print("⚠️  Some tests failed. Please address the issues above.")

if __name__ == "__main__":
    main()
